name = "cloudflare_temp_email"
main = "src/worker.ts"
compatibility_date = "2025-04-01"
compatibility_flags = [ "nodejs_compat" ]
# if you want use custom_domain, you need to add routes
# routes = [
# 	{ pattern = "temp-email-api.xxxxx.xyz", custom_domain = true },
# ]

# if you want deploy worker with frontend assets, you need to add assets
# [assets]
# directory = "../frontend/dist/"
# binding = "ASSETS"
# run_worker_first = true

# enable cron if you want set auto clean up
# [triggers]
# crons = [ "0 0 * * *" ]

# send_email = [
#    { name = "SEND_MAIL" },
# ]

[vars]
# DEFAULT_LANG = "zh"
# TITLE = "Custom Title" # custom title
# ANNOUNCEMENT = "Custom Announcement"
# always show ANNOUNCEMENT even no changes
# ALWAYS_SHOW_ANNOUNCEMENT = true
PREFIX = "tmp"
# address check REGEX, if not set, will not check
# ADDRESS_CHECK_REGEX = "^(?!.*admin).*"
# address name replace REG<PERSON>, if not set, the default is [^a-z0-9]
# ADDRESS_REGEX = "[^a-z0-9]"
# (min, max) length of the adderss, if not set, the default is (1, 30)
# MIN_ADDRESS_LEN = 1
# MAX_ADDRESS_LEN = 30
# IF YOU WANT TO MAKE YOUR SITE PRIVATE, UNCOMMENT THE FOLLOWING LINES
# PASSWORDS = ["123", "456"]
# For admin panel
# ADMIN_PASSWORDS = ["123", "456"]
# warning: no password or user check for admin portal
# DISABLE_ADMIN_PASSWORD_CHECK = false
# ADMIN CONTACT, CAN BE ANY STRING
# ADMIN_CONTACT = "<EMAIL>"
DEFAULT_DOMAINS = ["xxx.xxx1" , "xxx.xxx2"] # domain name for no role users
DOMAINS = ["xxx.xxx1" , "xxx.xxx2"] # all domain names
# For chinese domain name, you can use DOMAIN_LABELS to show chinese domain name
# DOMAIN_LABELS = ["中文.xxx", "xxx.xxx2"]
# USER_DEFAULT_ROLE = "vip" # default role for new users(only when enable mail verification)
# ADMIN_USER_ROLE = "admin" # the role which can access admin panel
# User roles configuration, if domains is empty will use default_domains, if prefix is null will use default prefix, if prefix is empty string will not use prefix
# USER_ROLES = [
#    { domains = ["xxx.xxx1" , "xxx.xxx2"], role = "vip", prefix = "vip" },
#    { domains = ["xxx.xxx1" , "xxx.xxx2"], role = "admin", prefix = "" },
# ]
JWT_SECRET = "xxx"
BLACK_LIST = ""
# Allow users to create email addresses
ENABLE_USER_CREATE_EMAIL = true
# Disable anonymous user create email, if set true, users can only create email addresses after logging in
# DISABLE_ANONYMOUS_USER_CREATE_EMAIL = true
# Allow users to delete messages
ENABLE_USER_DELETE_EMAIL = true
# Allow automatic replies to emails
ENABLE_AUTO_REPLY = false
# Allow webhook
# ENABLE_WEBHOOK = true
# Footer text
# COPYRIGHT = "Dream Hunter"
# DISABLE_SHOW_GITHUB = true
# default send balance, if not set, it will be 0
# DEFAULT_SEND_BALANCE = 1
# the role which can send emails without limit, multiple roles can be separated by ,
# NO_LIMIT_SEND_ROLE = "vip"
# Turnstile verification
# CF_TURNSTILE_SITE_KEY = ""
# CF_TURNSTILE_SECRET_KEY = ""
# telegram bot
# TG_MAX_ADDRESS = 5
# telegram bot info, predefined bot info can reduce latency of the webhook
# TG_BOT_INFO = "{}"
# global forward address list, if set, all emails will be forwarded to these addresses
# FORWARD_ADDRESS_LIST = ["<EMAIL>"]
# subdomain forward address list, if set, subdomain emails will be forwarded to these addresses
# SUBDOMAIN_FORWARD_ADDRESS_LIST = """
# [
#     {"domains":[""],"forward":"<EMAIL>"},
#     {"domains":["subdomain-1.domain.com","subdomain-2.domain.com"],"forward":"<EMAIL>"}
# ]
# """
# Frontend URL
# FRONTEND_URL = "https://xxxx.xxx"
# Enable check junk mail
# ENABLE_CHECK_JUNK_MAIL = false
# junk mail check list, if status exists and status is not pass, will be marked as junk mail
# JUNK_MAIL_CHECK_LIST = = ["spf", "dkim", "dmarc"]
# junk mail force check pass list, if no status or status is not pass, will be marked as junk mail
# JUNK_MAIL_FORCE_PASS_LIST = ["spf", "dkim", "dmarc"]
# remove attachment if size exceed 2MB, mail maybe mising some information due to parsing
# REMOVE_EXCEED_SIZE_ATTACHMENT = true
# remove all attachment, mail maybe mising some information due to parsing
# REMOVE_ALL_ATTACHMENT = true
# Calling other woker to process email
# ENABLE_ANOTHER_WORKER = false
# ANOTHER_WORKER_LIST = """
# [
#    {
#        "binding":"AUTH_INBOX",
#        "method":"rpcEmail",
#        "keywords":[
#            "验证码","激活码","激活链接","确认链接","验证邮箱","确认邮件","账号激活","邮件验证","账户确认","安全码","认证码","安全验证","登陆码","确认码","启用账户","激活账户","账号验证","注册确认",
#            "account","activation","verify","verification","activate","confirmation","email","code","validate","registration","login","code","expire","confirm"
#        ]
#    }
# ]
# """

[[d1_databases]]
binding = "DB"
database_name = "xxx"
database_id = "xxx"

# kv config for send email verification code
# [[kv_namespaces]]
# binding = "KV"
# id = "xxxx"

# ratelimit config for /api/new_address
# [[unsafe.bindings]]
# name = "RATE_LIMITER"
# type = "ratelimit"
# namespace_id = "1001"
# # 10 requests per minute
# simple = { limit = 10, period = 60 }

# binding another worker service (parse the code or link), e.g. auth-inbox
# [[services]]
# binding = "AUTH_INBOX"
# service = "auth-inbox"
