name: Deploy Backend

on:
  workflow_run:
    workflows: [Upstream Sync]
    types: [completed]
  push:
    tags:
      - "*"
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v3
        name: Install pnpm
        id: pnpm-install
        with:
          version: 8
          run_install: false

      - name: Deploy Backend for ${{ github.ref_name }}
        run: |
          export use_worker_assets=${{ secrets.USE_WORKER_ASSETS }}
          if [ -n "$use_worker_assets" ]; then
            cd frontend/
            pnpm install --no-frozen-lockfile
            pnpm build:pages
            cd ..
          fi

          export debug_mode=${{ secrets.DEBUG_MODE }}
          export use_mail_wasm_parser=${{ secrets.BACKEND_USE_MAIL_WASM_PARSER }}
          cd worker/
          echo '${{ secrets.BACKEND_TOML }}' > wrangler.toml
          pnpm install --no-frozen-lockfile

          if [ -n "$use_mail_wasm_parser" ]; then
            echo "Using mail-parser-wasm-worker"
            pnpm add mail-parser-wasm-worker
            git apply ../.github/config/mail-parser-wasm-worker.patch
            echo "Applied mail-parser-wasm-worker patch"
          fi

          if [ -n "$debug_mode" ]; then
            pnpm run deploy
          else
            output=$(pnpm run deploy 2>&1)
            if [ $? -ne 0 ]; then
                code=$?
                echo "Command failed with exit code $code"
                exit $code
            fi
          fi
          echo "Deployed for tag ${{ github.ref_name }}"
        env:
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
